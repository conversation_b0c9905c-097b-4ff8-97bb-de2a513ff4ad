import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:water_metering/domain/entities/auth_result.dart';
import 'package:water_metering/domain/services/biometric_service.dart';
import 'package:water_metering/presentation/widgets/auth/biometric_button.dart';
import 'package:water_metering/theme/theme2.dart';

import '../../../helpers/test_fixtures.dart';

// Mock classes
class MockBiometricService extends Mock implements BiometricService {}

class MockThemeNotifier extends Mock implements ThemeNotifier {}

void main() {
  group('BiometricButton Widget Tests', () {
    late MockBiometricService mockBiometricService;
    late MockThemeNotifier mockThemeNotifier;

    setUpAll(() {
      registerFallbackValue(const BiometricFailure.notAvailable());
    });

    setUp(() {
      mockBiometricService = MockBiometricService();
      mockThemeNotifier = MockThemeNotifier();

      // Setup default theme mock
      when(() => mockThemeNotifier.currentTheme).thenReturn(
        AppTheme(
          basicAdvanceTextColor: Colors.blue,
          // Add other required theme properties as needed
        ),
      );
    });

    Widget createTestWidget({
      VoidCallback? onSuccess,
      ValueChanged<String>? onError,
      String? text,
      String reason = 'Please authenticate to continue',
      bool enabled = true,
      bool showWhenUnavailable = false,
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) => MultiProvider(
          providers: [
            Provider<BiometricService>.value(value: mockBiometricService),
            ChangeNotifierProvider<ThemeNotifier>.value(
                value: mockThemeNotifier),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: BiometricButton(
                  onSuccess: onSuccess,
                  onError: onError,
                  text: text,
                  reason: reason,
                  enabled: enabled,
                  showWhenUnavailable: showWhenUnavailable,
                ),
              ),
            ),
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render biometric button with default text',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Allow async operations to complete

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.text('LOGIN WITH BIOMETRICS'), findsOneWidget);
        expect(find.byIcon(Icons.fingerprint), findsOneWidget);
      });

      testWidgets('should render custom text when provided', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget(
          text: 'Custom Biometric Text',
        ));
        await tester.pump();

        expect(find.text('Custom Biometric Text'), findsOneWidget);
      });

      testWidgets('should show loading indicator when authenticating',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          // Simulate a delay to test loading state
          await Future.delayed(const Duration(milliseconds: 100));
          return  AuthResult.success(user: null);
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Tap the button to start authentication
        await tester.tap(find.byType(GestureDetector));
        await tester.pump(); // Trigger loading state

        // Should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.byIcon(Icons.fingerprint), findsNothing);

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 200));
      });

      testWidgets(
          'should not render when biometrics unavailable and showWhenUnavailable is false',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: false,
        ));
        await tester.pump();

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.byType(GestureDetector), findsNothing);
      });

      testWidgets(
          'should render when biometrics unavailable and showWhenUnavailable is true',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: true,
        ));
        await tester.pump();

        expect(find.byType(BiometricButton), findsOneWidget);
        expect(find.byType(GestureDetector), findsOneWidget);
      });
    });

    group('Biometric Authentication', () {
      testWidgets('should call onSuccess when authentication succeeds',
          (tester) async {
        bool successCalled = false;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async => const AuthResult.success(user: null));

        await tester.pumpWidget(createTestWidget(
          onSuccess: () {
            successCalled = true;
          },
        ));
        await tester.pump();

        // Tap the button
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 100));

        expect(successCalled, isTrue);
      });

      testWidgets('should call onError when authentication fails',
          (tester) async {
        String? errorMessage;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async => const AuthResult.failure(
                  failure: BiometricFailure.authenticationFailed(),
                ));

        await tester.pumpWidget(createTestWidget(
          onError: (error) {
            errorMessage = error;
          },
        ));
        await tester.pump();

        // Tap the button
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 100));

        expect(errorMessage, isNotNull);
        expect(errorMessage, contains('Authentication failed'));
      });

      testWidgets('should handle authentication exceptions', (tester) async {
        String? errorMessage;

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenThrow(Exception('Biometric service error'));

        await tester.pumpWidget(createTestWidget(
          onError: (error) {
            errorMessage = error;
          },
        ));
        await tester.pump();

        // Tap the button
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Wait for error handling
        await tester.pump(const Duration(milliseconds: 100));

        expect(errorMessage, isNotNull);
        expect(errorMessage, contains('Biometric authentication failed'));
      });

      testWidgets('should pass custom reason to biometric service',
          (tester) async {
        const customReason = 'Custom authentication reason';

        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() => mockBiometricService.authenticate(reason: customReason))
            .thenAnswer((_) async => const AuthResult.success(user: null));

        await tester.pumpWidget(createTestWidget(
          reason: customReason,
        ));
        await tester.pump();

        // Tap the button
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Verify the custom reason was passed
        verify(() => mockBiometricService.authenticate(reason: customReason))
            .called(1);
      });
    });

    group('Button States', () {
      testWidgets('should be disabled when enabled is false', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget(
          enabled: false,
        ));
        await tester.pump();

        // Button should be rendered but disabled (with reduced opacity)
        expect(find.byType(GestureDetector), findsOneWidget);

        // Tap should not trigger authentication
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        verifyNever(() =>
            mockBiometricService.authenticate(reason: any(named: 'reason')));
      });

      testWidgets('should be disabled when biometrics not available',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => false);

        await tester.pumpWidget(createTestWidget(
          showWhenUnavailable: true,
        ));
        await tester.pump();

        // Tap should not trigger authentication
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        verifyNever(() =>
            mockBiometricService.authenticate(reason: any(named: 'reason')));
      });

      testWidgets('should be disabled during authentication', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return const AuthResult.success(user: null);
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Start authentication
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Try to tap again while loading
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Should only call authenticate once
        verify(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .called(1);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle different biometric failure types',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        // Test different failure types
        final failures = [
          const BiometricFailure.notAvailable(),
          const BiometricFailure.notEnrolled(),
          const BiometricFailure.authenticationFailed(),
          const BiometricFailure.userCancelled(),
          const BiometricFailure.unknown('Unknown error'),
        ];

        for (final failure in failures) {
          String? errorMessage;

          when(() => mockBiometricService.authenticate(
                  reason: any(named: 'reason')))
              .thenAnswer((_) async => AuthResult.failure(failure: failure));

          await tester.pumpWidget(createTestWidget(
            onError: (error) {
              errorMessage = error;
            },
          ));
          await tester.pump();

          // Tap the button
          await tester.tap(find.byType(GestureDetector));
          await tester.pump();

          // Wait for error handling
          await tester.pump(const Duration(milliseconds: 100));

          expect(errorMessage, isNotNull);
        }
      });
    });

    group('Widget Lifecycle', () {
      testWidgets('should handle widget disposal during authentication',
          (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);
        when(() =>
                mockBiometricService.authenticate(reason: any(named: 'reason')))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 200));
          return const AuthResult.success(user: null);
        });

        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Start authentication
        await tester.tap(find.byType(GestureDetector));
        await tester.pump();

        // Remove the widget while authentication is in progress
        await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        await tester.pump();

        // Wait for authentication to complete
        await tester.pump(const Duration(milliseconds: 300));

        // Should not throw any exceptions
      });
    });

    group('Accessibility', () {
      testWidgets('should be accessible for screen readers', (tester) async {
        when(() => mockBiometricService.isAvailable())
            .thenAnswer((_) async => true);

        await tester.pumpWidget(createTestWidget());
        await tester.pump();

        // Check that the button has semantic information
        expect(find.byType(GestureDetector), findsOneWidget);
        expect(find.text('LOGIN WITH BIOMETRICS'), findsOneWidget);
      });
    });
  });
}
