import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/domain/usecases/logout_usecase.dart';
import 'package:water_metering/domain/usecases/global_logout_usecase.dart';

import '../../helpers/test_helper.dart';

/// Unit tests for LogoutUseCase
///
/// These tests verify the business logic for user logout operations
/// including standard logout and global logout functionality.
void main() {
  group('LogoutUseCase', () {
    late LogoutUseCase logoutUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      logoutUseCase = LogoutUseCase(mockAuthRepository);
    });

    group('call', () {
      test('should complete successfully when logout succeeds', () async {
        // Arrange
        when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});

        // Act & Assert
        expect(() => logoutUseCase.call(), returnsNormally);

        // Verify repository was called
        verify(() => mockAuthRepository.logout()).called(1);
      });

      test('should propagate exceptions from repository', () async {
        // Arrange
        when(() => mockAuthRepository.logout())
            .thenThrow(Exception('Logout failed'));

        // Act & Assert
        expect(
          () => logoutUseCase.call(),
          throwsA(isA<Exception>()),
        );

        verify(() => mockAuthRepository.logout()).called(1);
      });

      test('should handle multiple consecutive logout calls', () async {
        // Arrange
        when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});

        // Act
        await logoutUseCase.call();
        await logoutUseCase.call();
        await logoutUseCase.call();

        // Assert
        verify(() => mockAuthRepository.logout()).called(3);
      });

      test('should handle concurrent logout calls', () async {
        // Arrange
        when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});

        // Act - Simulate concurrent calls
        final futures = List.generate(3, (_) => logoutUseCase.call());
        await Future.wait(futures);

        // Assert
        verify(() => mockAuthRepository.logout()).called(3);
      });
    });
  });

  group('GlobalLogoutUseCase', () {
    late GlobalLogoutUseCase globalLogoutUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      globalLogoutUseCase = GlobalLogoutUseCase(mockAuthRepository);
    });

    group('call', () {
      test('should complete successfully when global logout succeeds',
          () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenAnswer((_) async => {});

        // Act & Assert
        expect(() => globalLogoutUseCase.call(), returnsNormally);

        // Verify repository was called
        verify(() => mockAuthRepository.globalLogout()).called(1);
      });

      test('should propagate exceptions from repository', () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenThrow(Exception('Global logout failed'));

        // Act & Assert
        expect(
          () => globalLogoutUseCase.call(),
          throwsA(isA<Exception>()),
        );

        verify(() => mockAuthRepository.globalLogout()).called(1);
      });

      test('should handle multiple consecutive global logout calls', () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenAnswer((_) async => {});

        // Act
        await globalLogoutUseCase.call();
        await globalLogoutUseCase.call();
        await globalLogoutUseCase.call();

        // Assert
        verify(() => mockAuthRepository.globalLogout()).called(3);
      });

      test('should handle concurrent global logout calls', () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenAnswer((_) async => {});

        // Act - Simulate concurrent calls
        final futures = List.generate(3, (_) => globalLogoutUseCase.call());
        await Future.wait(futures);

        // Assert
        verify(() => mockAuthRepository.globalLogout()).called(3);
      });

      test('should handle network timeouts gracefully', () async {
        // Arrange
        when(() => mockAuthRepository.globalLogout())
            .thenThrow(Exception('Network timeout'));

        // Act & Assert
        expect(
          () => globalLogoutUseCase.call(),
          throwsA(isA<Exception>()),
        );

        verify(() => mockAuthRepository.globalLogout()).called(1);
      });
    });
  });

  group('Use Case Comparison', () {
    late LogoutUseCase logoutUseCase;
    late GlobalLogoutUseCase globalLogoutUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      logoutUseCase = LogoutUseCase(mockAuthRepository);
      globalLogoutUseCase = GlobalLogoutUseCase(mockAuthRepository);
    });

    test('should call different repository methods', () async {
      // Arrange
      when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});
      when(() => mockAuthRepository.globalLogout()).thenAnswer((_) async => {});

      // Act
      await logoutUseCase.call();
      await globalLogoutUseCase.call();

      // Assert
      verify(() => mockAuthRepository.logout()).called(1);
      verify(() => mockAuthRepository.globalLogout()).called(1);
    });

    test('should be independent of each other', () async {
      // Arrange
      when(() => mockAuthRepository.logout()).thenAnswer((_) async => {});
      when(() => mockAuthRepository.globalLogout())
          .thenThrow(Exception('Global logout failed'));

      // Act & Assert
      // Regular logout should succeed
      expect(() => logoutUseCase.call(), returnsNormally);

      // Global logout should fail
      expect(
        () => globalLogoutUseCase.call(),
        throwsA(isA<Exception>()),
      );

      verify(() => mockAuthRepository.logout()).called(1);
      verify(() => mockAuthRepository.globalLogout()).called(1);
    });

    test('should handle mixed success and failure scenarios', () async {
      // Arrange
      when(() => mockAuthRepository.logout())
          .thenThrow(Exception('Regular logout failed'));
      when(() => mockAuthRepository.globalLogout()).thenAnswer((_) async => {});

      // Act & Assert
      // Regular logout should fail
      expect(
        () => logoutUseCase.call(),
        throwsA(isA<Exception>()),
      );

      // Global logout should succeed
      expect(() => globalLogoutUseCase.call(), returnsNormally);

      verify(() => mockAuthRepository.logout()).called(1);
      verify(() => mockAuthRepository.globalLogout()).called(1);
    });
  });

  group('Error Handling', () {
    late LogoutUseCase logoutUseCase;
    late GlobalLogoutUseCase globalLogoutUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      logoutUseCase = LogoutUseCase(mockAuthRepository);
      globalLogoutUseCase = GlobalLogoutUseCase(mockAuthRepository);
    });

    test('should handle repository returning null', () async {
      // Arrange
      when(() => mockAuthRepository.logout()).thenAnswer((_) async => null);
      when(() => mockAuthRepository.globalLogout())
          .thenAnswer((_) async => null);

      // Act & Assert
      expect(() => logoutUseCase.call(), returnsNormally);
      expect(() => globalLogoutUseCase.call(), returnsNormally);
    });

    test('should handle various exception types', () async {
      // Arrange
      when(() => mockAuthRepository.logout())
          .thenThrow(ArgumentError('Invalid argument'));
      when(() => mockAuthRepository.globalLogout())
          .thenThrow(StateError('Invalid state'));

      // Act & Assert
      expect(
        () => logoutUseCase.call(),
        throwsA(isA<ArgumentError>()),
      );

      expect(
        () => globalLogoutUseCase.call(),
        throwsA(isA<StateError>()),
      );
    });

    test('should handle async exceptions properly', () async {
      // Arrange
      when(() => mockAuthRepository.logout()).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 10));
        throw Exception('Delayed failure');
      });

      // Act & Assert
      expect(
        () => logoutUseCase.call(),
        throwsA(isA<Exception>()),
      );
    });
  });
}
