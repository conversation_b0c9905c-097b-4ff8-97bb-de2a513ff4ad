import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/core/error/auth_failures.dart';

import 'package:water_metering/domain/usecases/verify_two_factor_usecase.dart';

import '../../helpers/test_fixtures.dart';
import '../../helpers/test_helper.dart';

/// Unit tests for VerifyTwoFactorUseCase
///
/// These tests verify the business logic for two-factor authentication
/// verification including input validation, code format validation,
/// and proper delegation to the repository layer.
void main() {
  group('VerifyTwoFactorUseCase', () {
    late VerifyTwoFactorUseCase verifyTwoFactorUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      verifyTwoFactorUseCase = VerifyTwoFactorUseCase(mockAuthRepository);
    });

    group('call', () {
      test('should return successful result when 2FA verification succeeds',
          () async {
        // Arrange
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await verifyTwoFactorUseCase.call(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isTrue);
        expect(result.user, isNotNull);
        expect(result.accessToken, isNotNull);
        verify(() => mockAuthRepository.verifyTwoFactor(
              TestFixtures.testTwoFactorRefCode,
              TestFixtures.testTwoFactorCode,
            )).called(1);
      });

      test('should trim whitespace from inputs', () async {
        // Arrange
        const refCodeWithSpaces = '  ${TestFixtures.testTwoFactorRefCode}  ';
        const codeWithSpaces = '  ${TestFixtures.testTwoFactorCode}  ';
        final expectedResult = TestFixtures.createSuccessAuthResult();
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        await verifyTwoFactorUseCase.call(refCodeWithSpaces, codeWithSpaces);

        // Assert
        verify(() => mockAuthRepository.verifyTwoFactor(
              TestFixtures.testTwoFactorRefCode,
              TestFixtures.testTwoFactorCode,
            )).called(1);
      });

      test('should return failure when 2FA code is invalid', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: const InvalidTwoFactorCodeFailure(),
        );
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await verifyTwoFactorUseCase.call(
          TestFixtures.testTwoFactorRefCode,
          TestFixtures.invalidTwoFactorCode,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<InvalidTwoFactorCodeFailure>());
      });

      test('should return failure when reference code is expired', () async {
        // Arrange
        final expectedResult = TestFixtures.createFailureAuthResult(
          failure: const TwoFactorExpiredFailure(),
        );
        when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await verifyTwoFactorUseCase.call(
          TestFixtures.expiredTwoFactorRefCode,
          TestFixtures.testTwoFactorCode,
        );

        // Assert
        expect(result, equals(expectedResult));
        expect(result.success, isFalse);
        expect(result.failure, isA<TwoFactorExpiredFailure>());
      });

      group('input validation', () {
        test('should return failure for empty reference code', () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            '',
            TestFixtures.testTwoFactorCode,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('reference code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should return failure for empty verification code', () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '',
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('verification code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should return failure for whitespace-only reference code',
            () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            '   ',
            TestFixtures.testTwoFactorCode,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('reference code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should return failure for whitespace-only verification code',
            () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '   ',
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('verification code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should return failure for invalid verification code format',
            () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            'abc123', // Invalid format - should be 6 digits
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('verification code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should return failure for verification code with wrong length',
            () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '12345', // Too short
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          expect(result.failure?.message, contains('verification code'));
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should accept valid 6-digit verification code', () async {
          // Arrange
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '123456',
          );

          // Assert
          expect(result.success, isTrue);
          verify(() => mockAuthRepository.verifyTwoFactor(
                TestFixtures.testTwoFactorRefCode,
                '123456',
              )).called(1);
        });
      });

      group('edge cases', () {
        test('should handle repository exceptions gracefully', () async {
          // Arrange
          when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
              .thenThrow(Exception('Network error'));

          // Act & Assert
          expect(
            () => verifyTwoFactorUseCase.call(
              TestFixtures.testTwoFactorRefCode,
              TestFixtures.testTwoFactorCode,
            ),
            throwsA(isA<Exception>()),
          );
        });

        test('should handle malformed reference codes', () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            'invalid-ref-code-format',
            TestFixtures.testTwoFactorCode,
          );

          // Assert
          expect(result.success, isFalse);
          expect(result.failure, isA<ValidationFailure>());
          verifyNever(() => mockAuthRepository.verifyTwoFactor(any(), any()));
        });

        test('should handle numeric verification codes as strings', () async {
          // Arrange
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '000000', // All zeros should be valid
          );

          // Assert
          expect(result.success, isTrue);
          verify(() => mockAuthRepository.verifyTwoFactor(
                TestFixtures.testTwoFactorRefCode,
                '000000',
              )).called(1);
        });

        test('should handle verification codes with leading zeros', () async {
          // Arrange
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act
          final result = await verifyTwoFactorUseCase.call(
            TestFixtures.testTwoFactorRefCode,
            '001234',
          );

          // Assert
          expect(result.success, isTrue);
          verify(() => mockAuthRepository.verifyTwoFactor(
                TestFixtures.testTwoFactorRefCode,
                '001234',
              )).called(1);
        });
      });

      group('security considerations', () {
        test('should not expose sensitive information in validation errors',
            () async {
          // Act
          final result = await verifyTwoFactorUseCase.call(
            '',
            TestFixtures.testTwoFactorCode,
          );

          // Assert
          expect(result.failure?.message,
              isNot(contains(TestFixtures.testTwoFactorCode)));
          expect(result.failure?.message, isNot(contains('secret')));
          expect(result.failure?.message, isNot(contains('token')));
        });

        test('should handle concurrent verification attempts', () async {
          // Arrange
          final expectedResult = TestFixtures.createSuccessAuthResult();
          when(() => mockAuthRepository.verifyTwoFactor(any(), any()))
              .thenAnswer((_) async => expectedResult);

          // Act - Simulate concurrent calls
          final futures = List.generate(
              3,
              (_) => verifyTwoFactorUseCase.call(
                    TestFixtures.testTwoFactorRefCode,
                    TestFixtures.testTwoFactorCode,
                  ));
          final results = await Future.wait(futures);

          // Assert
          expect(results.length, equals(3));
          for (final result in results) {
            expect(result.success, isTrue);
          }
          verify(() => mockAuthRepository.verifyTwoFactor(
                TestFixtures.testTwoFactorRefCode,
                TestFixtures.testTwoFactorCode,
              )).called(3);
        });
      });
    });
  });
}
